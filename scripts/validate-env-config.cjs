#!/usr/bin/env node

// 环境变量配置验证脚本
const fs = require('fs');
const path = require('path');

console.log('🔍 验证环境变量配置...\n');

// 加载.env文件（如果存在）
const envPath = path.join(__dirname, '../.env');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
  console.log('✅ 发现 .env 文件，已加载配置');
} else {
  console.log('⚠️  未发现 .env 文件，使用系统环境变量');
}

// 定义配置分组 - 只包含实际使用的配置项
const configGroups = {
  '应用基础配置': {
    NODE_ENV: { required: false, default: 'development', description: '运行环境' },
    CORS_ORIGIN: { required: false, default: '*', description: 'CORS配置' }
  },

  '服务端口配置': {

    LDAP_AUTH_PORT: { required: false, default: '3002', description: 'LDAP认证端口' }
  },

  '前端API配置': {
    VITE_LDAP_API_URL: { required: false, default: 'http://localhost:3002', description: 'LDAP API地址' },
    VITE_CHAT_API_URL: { required: false, default: 'http://localhost:3001', description: '聊天API地址' },
    VITE_LITELLM_API_BASE: { required: false, default: 'http://localhost:4000', description: '前端LiteLLM地址' }
  },
  
  'LDAP认证配置': {
    LDAP_DEFAULT_HOST: { required: false, default: 'localhost', description: '默认LDAP主机' },
    LDAP_DEFAULT_PORT: { required: false, default: '389', description: '默认LDAP端口' },
    LDAP_DEFAULT_ENVIRONMENT: { required: false, default: '240.10云桌面环境', description: '默认LDAP环境' },
    LDAP_CONNECTION_TIMEOUT: { required: false, default: '5000', description: '连接超时时间' },
    LDAP_SEARCH_TIMEOUT: { required: false, default: '10000', description: '搜索超时时间' },
    LDAP_MAX_RETRIES: { required: false, default: '3', description: '最大重试次数' }
  },
  
  'LDAP环境配置': {
    'LDAP_240.10云桌面环境_URL': { required: true, description: '240.10云桌面LDAP地址' },
    'LDAP_240.10云桌面环境_BASE_DN': { required: true, description: '240.10云桌面Base DN' },
    'LDAP_242.2云桌面环境_URL': { required: true, description: '242.2云桌面LDAP地址' },
    'LDAP_242.2云桌面环境_BASE_DN': { required: true, description: '242.2云桌面Base DN' }
  },

  'LDAP测试端口配置': {
    LDAP_TEST_PORT: { required: false, default: '389', description: 'LDAP测试端口' },
    LDAPS_TEST_PORT: { required: false, default: '636', description: 'LDAPS测试端口' },
    LDAP_GC_PORT: { required: false, default: '3268', description: '全局目录端口' },
    LDAPS_GC_PORT: { required: false, default: '3269', description: 'LDAPS全局目录端口' }
  },
  
  'LiteLLM配置': {
    LITELLM_API_BASE: { required: true, description: 'LiteLLM API地址' },
    LITELLM_MASTER_KEY: { required: true, sensitive: true, description: 'LiteLLM主密钥' }
  },

  'LLM模型配置': {
    OPENAI_API_KEY: { required: false, sensitive: true, description: 'OpenAI API密钥' },
    OPENAI_MODEL: { required: false, default: 'deepseek-reasoner', description: 'OpenAI模型' },
    OPENAI_BASE_URL: { required: false, default: 'http://***********:4000/v1', description: 'OpenAI基础URL' },
    ANTHROPIC_API_KEY: { required: false, sensitive: true, description: 'Anthropic API密钥' },
    ANTHROPIC_MODEL: { required: false, default: 'claude-3-sonnet-20240229', description: 'Anthropic模型' },
    AZURE_OPENAI_API_KEY: { required: false, sensitive: true, description: 'Azure OpenAI API密钥' },
    AZURE_OPENAI_ENDPOINT: { required: false, description: 'Azure OpenAI端点' }
  },

  'LLM功能开关': {
    ENABLE_OPENAI: { required: false, default: 'true', description: '启用OpenAI' },
    ENABLE_ANTHROPIC: { required: false, default: 'false', description: '启用Anthropic' },
    ENABLE_AZURE: { required: false, default: 'false', description: '启用Azure' }
  },

  '性能配置': {
    LLM_TIMEOUT: { required: false, default: '30000', description: 'LLM超时时间(毫秒)' },
    LLM_MAX_TOKENS: { required: false, default: '4000', description: '最大Token数' },
    LLM_TEMPERATURE: { required: false, default: '0.7', description: '生成温度' },
    API_RETRY_COUNT: { required: false, default: '2', description: 'API重试次数' },
    API_RETRY_DELAY: { required: false, default: '1000', description: 'API重试延迟(毫秒)' }
  }
};

// 验证配置
let totalConfigs = 0;
let validConfigs = 0;
let missingRequired = [];
let configStatus = {};

console.log('📊 配置验证结果:\n');

Object.keys(configGroups).forEach(groupName => {
  console.log(`🔷 ${groupName}:`);
  
  const configs = configGroups[groupName];
  Object.keys(configs).forEach(configKey => {
    totalConfigs++;
    const config = configs[configKey];
    const value = process.env[configKey];
    
    let status = '❌';
    let displayValue = '未设置';
    
    if (value) {
      validConfigs++;
      status = '✅';
      
      if (config.sensitive) {
        displayValue = value.length > 0 ? '***已设置***' : '未设置';
      } else {
        displayValue = value.length > 50 ? value.substring(0, 47) + '...' : value;
      }
    } else if (config.default) {
      status = '🔶';
      displayValue = `使用默认值: ${config.default}`;
    } else if (config.required) {
      missingRequired.push({ key: configKey, description: config.description });
    }
    
    configStatus[configKey] = { status, value: displayValue, config };
    
    console.log(`  ${status} ${configKey}`);
    console.log(`     ${config.description}: ${displayValue}`);
  });
  
  console.log('');
});

// 汇总统计
console.log('📈 配置统计:');
console.log(`  总配置项: ${totalConfigs}`);
console.log(`  已配置: ${validConfigs}`);
console.log(`  使用默认值: ${totalConfigs - validConfigs - missingRequired.length}`);
console.log(`  缺失必需项: ${missingRequired.length}`);

// 显示缺失的必需配置
if (missingRequired.length > 0) {
  console.log('\n❗ 缺失的必需配置:');
  missingRequired.forEach(item => {
    console.log(`  ❌ ${item.key} - ${item.description}`);
  });
}

// LDAP环境检查
console.log('\n🔧 LDAP环境检查:');
const ldapEnvs = Object.keys(process.env)
  .filter(key => key.startsWith('LDAP_') && key.includes('_URL'))
  .map(key => key.replace('LDAP_', '').replace('_URL', ''));

if (ldapEnvs.length > 0) {
  console.log(`  发现 ${ldapEnvs.length} 个LDAP环境:`);
  ldapEnvs.forEach(env => {
    console.log(`    - ${env}`);
  });
} else {
  console.log('  ⚠️  未发现LDAP环境配置');
}

// 服务连接检查建议
console.log('\n🔗 服务连接检查建议:');
console.log('  1. 验证LDAP服务器连接:');
console.log(`     node scripts/test-ldap-connection.cjs`);
console.log('  2. 验证LiteLLM服务连接:');
console.log(`     node scripts/test-litellm-connection.js`);

// 生成配置报告
console.log('\n📄 生成配置报告:');
const report = {
  timestamp: new Date().toISOString(),
  totalConfigs,
  validConfigs,
  missingRequired: missingRequired.length,
  ldapEnvironments: ldapEnvs,
  configStatus: Object.keys(configStatus).reduce((acc, key) => {
    acc[key] = {
      status: configStatus[key].status,
      hasValue: !!process.env[key],
      isRequired: configStatus[key].config.required || false
    };
    return acc;
  }, {})
};

const reportPath = path.join(__dirname, '../config-validation-report.json');
fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
console.log(`  ✅ 报告已保存到: ${reportPath}`);

// 最终状态
if (missingRequired.length === 0) {
  console.log('\n🎉 配置验证通过！所有必需配置都已设置');
} else {
  console.log('\n⚠️  配置验证警告：存在缺失的必需配置项');
  console.log('   请参考 env.example 文件设置缺失的配置');
}

console.log('\n💡 提示:');
console.log('  - 复制 env.example 为 .env 并修改配置值');
console.log('  - 生产环境请务必修改默认密钥和敏感信息');
console.log('  - 使用 npm install dotenv 来支持 .env 文件加载'); 
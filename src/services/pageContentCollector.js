/**
 * 页面内容收集器 - 收集当前页面的所有可见内容和状态信息
 * 为AI提供完整的页面上下文感知能力
 */

class PageContentCollector {
  constructor() {
    this.contentCache = new Map();
    this.observers = new Set();
    this.lastUpdate = null;
    this.updateInterval = null;
    this.isCollecting = false;
    this.userInteractionTracker = new Map();
    this.errorLog = [];
    
    // 初始化内容收集
    this.initializeCollection();
  }

  /**
   * 初始化内容收集
   */
  initializeCollection() {
    // 确保DOM已加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.startCollection();
      });
    } else {
      this.startCollection();
    }
  }

  /**
   * 开始收集页面内容
   */
  startCollection() {
    this.isCollecting = true;
    
    // 立即收集一次
    this.collectAllContent();
    
    // 设置定期更新
    this.updateInterval = setInterval(() => {
      this.collectAllContent();
    }, 5000); // 每5秒更新一次
    
    // 监听页面变化
    this.setupObservers();
    
    // 设置用户交互跟踪
    this.setupUserInteractionTracking();
    
    // 设置错误日志收集
    this.setupErrorLogging();
  }

  /**
   * 收集所有页面内容
   */
  collectAllContent() {
    try {
      const content = {
        timestamp: new Date().toISOString(),
        url: window.location.href,
        title: document.title,
        
        // 页面结构信息
        structure: this.collectPageStructure(),
        
        // 可见内容
        visibleContent: this.collectVisibleContent(),
        
        // 交互元素
        interactiveElements: this.collectInteractiveElements(),
        
        // 应用状态
        appState: this.collectAppState(),
        
        // 导航信息
        navigation: this.collectNavigationInfo(),
        
        // 媒体内容
        media: this.collectMediaContent(),
        
        // 性能信息
        performance: this.collectPerformanceInfo(),
        
        // 用户交互数据
        userInteraction: this.getUserInteractionSummary(),
        
        // 错误信息
        errors: this.getRecentErrors()
      };
      
      this.contentCache.set('current', content);
      this.lastUpdate = Date.now();
      
      // 触发更新事件
      this.dispatchUpdateEvent(content);
      
      return content;
    } catch (error) {
      console.error('页面内容收集失败:', error);
      this.logError('content_collection_failed', error);
      return null;
    }
  }

  /**
   * 收集页面结构信息
   */
  collectPageStructure() {
    const structure = {
      sections: [],
      landmarks: [],
      headings: []
    };

    // 收集页面区域
    const sections = document.querySelectorAll('section, div[id], main, article, aside, nav, header, footer');
    sections.forEach(section => {
      if (this.isElementVisible(section)) {
        structure.sections.push({
          id: section.id || null,
          tagName: section.tagName.toLowerCase(),
          className: section.className || null,
          textContent: section.textContent?.substring(0, 200) || '',
          position: this.getElementPosition(section)
        });
      }
    });

    // 收集地标元素
    const landmarks = document.querySelectorAll('[role], [aria-label], [aria-labelledby]');
    landmarks.forEach(landmark => {
      if (this.isElementVisible(landmark)) {
        structure.landmarks.push({
          role: landmark.getAttribute('role') || null,
          ariaLabel: landmark.getAttribute('aria-label') || null,
          ariaLabelledBy: landmark.getAttribute('aria-labelledby') || null,
          textContent: landmark.textContent?.substring(0, 100) || ''
        });
      }
    });

    // 收集标题
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      if (this.isElementVisible(heading)) {
        structure.headings.push({
          level: parseInt(heading.tagName.charAt(1)),
          text: heading.textContent?.trim() || '',
          id: heading.id || null
        });
      }
    });

    return structure;
  }

  /**
   * 收集可见内容
   */
  collectVisibleContent() {
    const content = {
      text: [],
      images: [],
      videos: [],
      lists: [],
      tables: [],
      codeBlocks: []
    };

    // 收集文本内容
    const textElements = document.querySelectorAll('p, span, div, article, section');
    textElements.forEach(el => {
      if (this.isElementVisible(el) && this.hasSignificantText(el)) {
        content.text.push({
          text: el.textContent?.trim().substring(0, 500) || '',
          tag: el.tagName.toLowerCase(),
          className: el.className || null,
          id: el.id || null
        });
      }
    });

    // 收集图片
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (this.isElementVisible(img)) {
        content.images.push({
          src: img.src,
          alt: img.alt || '',
          title: img.title || '',
          width: img.width,
          height: img.height
        });
      }
    });

    // 收集视频
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      if (this.isElementVisible(video)) {
        content.videos.push({
          src: video.src || video.currentSrc,
          poster: video.poster || '',
          duration: video.duration || 0,
          controls: video.controls
        });
      }
    });

    // 收集列表
    const lists = document.querySelectorAll('ul, ol');
    lists.forEach(list => {
      if (this.isElementVisible(list)) {
        const items = Array.from(list.querySelectorAll('li')).map(li => li.textContent?.trim() || '');
        content.lists.push({
          type: list.tagName.toLowerCase(),
          items: items.slice(0, 10), // 限制列表项数量
          className: list.className || null
        });
      }
    });

    // 收集表格
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
      if (this.isElementVisible(table)) {
        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent?.trim() || '');
        const rows = Array.from(table.querySelectorAll('tr')).slice(1, 6).map(row => {
          return Array.from(row.querySelectorAll('td')).map(td => td.textContent?.trim() || '');
        });
        content.tables.push({
          headers,
          rows,
          className: table.className || null
        });
      }
    });

    // 收集代码块
    const codeBlocks = document.querySelectorAll('code, pre');
    codeBlocks.forEach(code => {
      if (this.isElementVisible(code)) {
        content.codeBlocks.push({
          code: code.textContent?.substring(0, 300) || '',
          language: code.className?.match(/language-(\w+)/)?.[1] || 'text',
          tag: code.tagName.toLowerCase()
        });
      }
    });

    return content;
  }

  /**
   * 收集交互元素
   */
  collectInteractiveElements() {
    const elements = {
      buttons: [],
      links: [],
      forms: [],
      inputs: []
    };

    // 收集按钮
    const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], [role="button"]');
    buttons.forEach(button => {
      if (this.isElementVisible(button)) {
        elements.buttons.push({
          text: button.textContent?.trim() || button.value || '',
          type: button.type || 'button',
          disabled: button.disabled || false,
          className: button.className || null,
          id: button.id || null
        });
      }
    });

    // 收集链接
    const links = document.querySelectorAll('a[href]');
    links.forEach(link => {
      if (this.isElementVisible(link)) {
        elements.links.push({
          text: link.textContent?.trim() || '',
          href: link.href,
          target: link.target || '_self',
          className: link.className || null
        });
      }
    });

    // 收集表单
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      if (this.isElementVisible(form)) {
        elements.forms.push({
          action: form.action || '',
          method: form.method || 'GET',
          inputs: Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
            name: input.name || '',
            type: input.type || 'text',
            placeholder: input.placeholder || '',
            required: input.required || false
          }))
        });
      }
    });

    // 收集输入框
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      if (this.isElementVisible(input)) {
        elements.inputs.push({
          name: input.name || '',
          type: input.type || 'text',
          placeholder: input.placeholder || '',
          value: input.type === 'password' ? '[PASSWORD]' : (input.value || ''),
          required: input.required || false,
          disabled: input.disabled || false
        });
      }
    });

    return elements;
  }

  /**
   * 收集应用状态
   */
  collectAppState() {
    const state = {
      route: {
        path: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash
      },
      user: null,
      theme: null,
      loading: false,
      modals: [],
      notifications: []
    };

    // 检测用户状态
    const userIndicator = document.querySelector('[data-user-status], .user-info, .login-info');
    if (userIndicator) {
      state.user = {
        loggedIn: !userIndicator.textContent?.includes('登录'),
        username: this.extractUsernameFromElement(userIndicator)
      };
    }

    // 检测主题
    const themeIndicators = document.querySelectorAll('[data-theme], .theme-toggle, .dark-mode');
    themeIndicators.forEach(indicator => {
      if (indicator.textContent?.includes('暗') || indicator.classList.contains('dark')) {
        state.theme = 'dark';
      } else if (indicator.textContent?.includes('亮') || indicator.classList.contains('light')) {
        state.theme = 'light';
      }
    });

    // 检测加载状态
    const loadingIndicators = document.querySelectorAll('.loading, .spinner, [data-loading]');
    state.loading = loadingIndicators.length > 0;

    // 检测模态框
    const modals = document.querySelectorAll('.modal, .dialog, [role="dialog"]');
    modals.forEach(modal => {
      if (this.isElementVisible(modal)) {
        state.modals.push({
          title: modal.querySelector('.modal-title, .dialog-title')?.textContent?.trim() || '',
          content: modal.textContent?.substring(0, 200) || '',
          className: modal.className || null
        });
      }
    });

    // 检测通知
    const notifications = document.querySelectorAll('.notification, .alert, .toast, [role="alert"]');
    notifications.forEach(notification => {
      if (this.isElementVisible(notification)) {
        state.notifications.push({
          type: this.getNotificationType(notification),
          message: notification.textContent?.trim() || '',
          className: notification.className || null
        });
      }
    });

    return state;
  }

  /**
   * 收集导航信息
   */
  collectNavigationInfo() {
    const navigation = {
      menu: [],
      breadcrumbs: [],
      pagination: null,
      tabs: []
    };

    // 收集菜单
    const menus = document.querySelectorAll('nav, .nav, .menu, [role="navigation"]');
    menus.forEach(menu => {
      if (this.isElementVisible(menu)) {
        const items = Array.from(menu.querySelectorAll('a, button, .nav-item')).map(item => ({
          text: item.textContent?.trim() || '',
          href: item.href || null,
          active: item.classList.contains('active') || item.getAttribute('aria-current') === 'page'
        }));
        navigation.menu.push({
          items: items.slice(0, 20), // 限制菜单项数量
          className: menu.className || null
        });
      }
    });

    // 收集面包屑
    const breadcrumbs = document.querySelectorAll('.breadcrumb, .breadcrumbs, [aria-label*="面包屑"]');
    breadcrumbs.forEach(breadcrumb => {
      if (this.isElementVisible(breadcrumb)) {
        const items = Array.from(breadcrumb.querySelectorAll('a, span, .breadcrumb-item')).map(item => ({
          text: item.textContent?.trim() || '',
          href: item.href || null
        }));
        navigation.breadcrumbs.push(items);
      }
    });

    // 收集分页
    const pagination = document.querySelector('.pagination, .pager, [aria-label*="分页"]');
    if (pagination && this.isElementVisible(pagination)) {
      navigation.pagination = {
        current: pagination.querySelector('.current, .active')?.textContent?.trim() || '',
        total: pagination.querySelectorAll('a, button').length,
        hasNext: !!pagination.querySelector('.next, [aria-label*="下一"]'),
        hasPrev: !!pagination.querySelector('.prev, .previous, [aria-label*="上一"]')
      };
    }

    // 收集标签页
    const tabs = document.querySelectorAll('.tabs, .tab-list, [role="tablist"]');
    tabs.forEach(tabList => {
      if (this.isElementVisible(tabList)) {
        const items = Array.from(tabList.querySelectorAll('[role="tab"], .tab')).map(tab => ({
          text: tab.textContent?.trim() || '',
          active: tab.getAttribute('aria-selected') === 'true' || tab.classList.contains('active'),
          disabled: tab.getAttribute('aria-disabled') === 'true' || tab.classList.contains('disabled')
        }));
        navigation.tabs.push(items);
      }
    });

    return navigation;
  }

  /**
   * 收集媒体内容
   */
  collectMediaContent() {
    const media = {
      totalImages: 0,
      totalVideos: 0,
      totalAudios: 0,
      imageTypes: new Set(),
      videoTypes: new Set(),
      audioTypes: new Set()
    };

    // 统计图片
    const images = document.querySelectorAll('img');
    media.totalImages = images.length;
    images.forEach(img => {
      const type = img.src.split('.').pop()?.toLowerCase();
      if (type) media.imageTypes.add(type);
    });

    // 统计视频
    const videos = document.querySelectorAll('video');
    media.totalVideos = videos.length;
    videos.forEach(video => {
      const type = video.src?.split('.').pop()?.toLowerCase();
      if (type) media.videoTypes.add(type);
    });

    // 统计音频
    const audios = document.querySelectorAll('audio');
    media.totalAudios = audios.length;
    audios.forEach(audio => {
      const type = audio.src?.split('.').pop()?.toLowerCase();
      if (type) media.audioTypes.add(type);
    });

    return {
      totalImages: media.totalImages,
      totalVideos: media.totalVideos,
      totalAudios: media.totalAudios,
      imageTypes: Array.from(media.imageTypes),
      videoTypes: Array.from(media.videoTypes),
      audioTypes: Array.from(media.audioTypes)
    };
  }

  /**
   * 收集性能信息
   */
  collectPerformanceInfo() {
    const performance = {
      loadTime: null,
      domElements: document.querySelectorAll('*').length,
      scripts: document.querySelectorAll('script').length,
      stylesheets: document.querySelectorAll('link[rel="stylesheet"]').length,
      memoryUsage: null,
      networkRequests: null
    };

    // 获取加载时间
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      performance.loadTime = timing.loadEventEnd - timing.navigationStart;
    }

    // 获取内存使用情况
    if (window.performance && window.performance.memory) {
      performance.memoryUsage = {
        used: window.performance.memory.usedJSHeapSize,
        total: window.performance.memory.totalJSHeapSize,
        limit: window.performance.memory.jsHeapSizeLimit
      };
    }

    // 获取网络请求数量
    if (window.performance && window.performance.getEntriesByType) {
      const resources = window.performance.getEntriesByType('resource');
      performance.networkRequests = resources.length;
    }

    return performance;
  }

  /**
   * 设置页面变化监听器
   */
  setupObservers() {
    // DOM 变化监听器
    const observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          shouldUpdate = true;
        }
        if (mutation.type === 'attributes' && 
            ['class', 'style', 'hidden', 'aria-hidden'].includes(mutation.attributeName)) {
          shouldUpdate = true;
        }
      });
      
      if (shouldUpdate) {
        // 防抖更新
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
          this.collectAllContent();
        }, 1000);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style', 'hidden', 'aria-hidden']
    });

    this.observers.add(observer);

    // 视口变化监听器
    const resizeObserver = new ResizeObserver(() => {
      this.collectAllContent();
    });

    resizeObserver.observe(document.body);
    this.observers.add(resizeObserver);
  }

  /**
   * 设置用户交互跟踪
   */
  setupUserInteractionTracking() {
    const events = ['click', 'scroll', 'focus', 'blur', 'input'];
    
    events.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        const timestamp = Date.now();
        const target = event.target;
        
        const interaction = {
          type: eventType,
          timestamp,
          element: {
            tagName: target.tagName,
            className: target.className || null,
            id: target.id || null,
            textContent: target.textContent?.substring(0, 100) || ''
          }
        };

        // 只保留最近的交互记录
        if (!this.userInteractionTracker.has(eventType)) {
          this.userInteractionTracker.set(eventType, []);
        }
        
        const interactions = this.userInteractionTracker.get(eventType);
        interactions.push(interaction);
        
        // 只保留最近20个交互
        if (interactions.length > 20) {
          interactions.shift();
        }
      });
    });
  }

  /**
   * 设置错误日志收集
   */
  setupErrorLogging() {
    // JavaScript错误
    window.addEventListener('error', (event) => {
      this.logError('javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.toString()
      });
    });

    // 网络错误
    window.addEventListener('unhandledrejection', (event) => {
      this.logError('promise_rejection', {
        reason: event.reason?.toString()
      });
    });

    // 控制台错误
    const originalConsoleError = console.error;
    console.error = (...args) => {
      this.logError('console_error', {
        message: args.map(arg => arg?.toString()).join(' ')
      });
      originalConsoleError.apply(console, args);
    };
  }

  /**
   * 记录错误
   */
  logError(type, details) {
    const error = {
      type,
      details,
      timestamp: Date.now(),
      url: window.location.href
    };

    this.errorLog.push(error);
    
    // 只保留最近10个错误
    if (this.errorLog.length > 10) {
      this.errorLog.shift();
    }
  }

  /**
   * 获取用户交互摘要
   */
  getUserInteractionSummary() {
    const summary = {
      totalInteractions: 0,
      recentInteractions: [],
      interactionTypes: {}
    };

    for (const [type, interactions] of this.userInteractionTracker) {
      summary.totalInteractions += interactions.length;
      summary.interactionTypes[type] = interactions.length;
      
      // 添加最近的交互
      const recent = interactions.slice(-3);
      summary.recentInteractions.push(...recent);
    }

    // 按时间排序最近的交互
    summary.recentInteractions.sort((a, b) => b.timestamp - a.timestamp);
    summary.recentInteractions = summary.recentInteractions.slice(0, 10);

    return summary;
  }

  /**
   * 获取最近的错误
   */
  getRecentErrors() {
    return this.errorLog.slice(-5);
  }

  /**
   * 工具方法：检查元素是否可见
   */
  isElementVisible(element) {
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);
    
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0'
    );
  }

  /**
   * 工具方法：检查元素是否有重要文本
   */
  hasSignificantText(element) {
    const text = element.textContent?.trim() || '';
    return text.length > 10 && text.length < 1000;
  }

  /**
   * 工具方法：获取元素位置
   */
  getElementPosition(element) {
    const rect = element.getBoundingClientRect();
    return {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width,
      height: rect.height
    };
  }

  /**
   * 工具方法：从元素中提取用户名
   */
  extractUsernameFromElement(element) {
    const text = element.textContent?.trim() || '';
    const match = text.match(/用户[：:]\s*([^\s]+)|([^\s]+)(?:\s*已登录)/);
    return match ? (match[1] || match[2]) : null;
  }

  /**
   * 工具方法：获取通知类型
   */
  getNotificationType(element) {
    const className = element.className.toLowerCase();
    
    if (className.includes('error') || className.includes('danger')) return 'error';
    if (className.includes('warning') || className.includes('warn')) return 'warning';
    if (className.includes('success')) return 'success';
    if (className.includes('info')) return 'info';
    
    return 'default';
  }

  /**
   * 触发更新事件
   */
  dispatchUpdateEvent(content) {
    const event = new CustomEvent('pageContentUpdated', {
      detail: content
    });
    window.dispatchEvent(event);
  }

  /**
   * 获取当前页面内容
   */
  getCurrentContent() {
    return this.contentCache.get('current') || this.collectAllContent();
  }

  /**
   * 获取页面内容摘要
   */
  getContentSummary() {
    const content = this.getCurrentContent();
    if (!content) return null;

    return {
      title: content.title,
      url: content.url,
      sectionsCount: content.structure.sections.length,
      headingsCount: content.structure.headings.length,
      buttonsCount: content.interactiveElements.buttons.length,
      linksCount: content.interactiveElements.links.length,
      imagesCount: content.media.totalImages,
      userLoggedIn: content.appState.user?.loggedIn || false,
      hasModals: content.appState.modals.length > 0,
      hasNotifications: content.appState.notifications.length > 0,
      recentInteractions: content.userInteraction.totalInteractions,
      errors: content.errors.length,
      lastUpdate: content.timestamp
    };
  }

  /**
   * 搜索页面内容
   */
  searchContent(query) {
    const content = this.getCurrentContent();
    if (!content) return [];

    const results = [];
    const queryLower = query.toLowerCase();

    // 搜索标题
    content.structure.headings.forEach(heading => {
      if (heading.text.toLowerCase().includes(queryLower)) {
        results.push({
          type: 'heading',
          content: heading.text,
          context: `${heading.level}级标题`,
          relevance: 0.9
        });
      }
    });

    // 搜索可见文本
    content.visibleContent.text.forEach(text => {
      if (text.text.toLowerCase().includes(queryLower)) {
        results.push({
          type: 'text',
          content: text.text.substring(0, 200),
          context: `${text.tag}元素`,
          relevance: 0.7
        });
      }
    });

    // 搜索按钮
    content.interactiveElements.buttons.forEach(button => {
      if (button.text.toLowerCase().includes(queryLower)) {
        results.push({
          type: 'button',
          content: button.text,
          context: '可点击按钮',
          relevance: 0.8
        });
      }
    });

    // 搜索链接
    content.interactiveElements.links.forEach(link => {
      if (link.text.toLowerCase().includes(queryLower)) {
        results.push({
          type: 'link',
          content: link.text,
          context: `链接到: ${link.href}`,
          relevance: 0.8
        });
      }
    });

    return results.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * 停止收集
   */
  stopCollection() {
    this.isCollecting = false;
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    
    this.observers.forEach(observer => {
      observer.disconnect();
    });
    this.observers.clear();
  }

  /**
   * 重置收集器
   */
  reset() {
    this.stopCollection();
    this.contentCache.clear();
    this.userInteractionTracker.clear();
    this.errorLog.length = 0;
    this.lastUpdate = null;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      isCollecting: this.isCollecting,
      lastUpdate: this.lastUpdate,
      cacheSize: this.contentCache.size,
      observersCount: this.observers.size,
      interactionTypes: Array.from(this.userInteractionTracker.keys()),
      errorCount: this.errorLog.length,
      totalInteractions: Array.from(this.userInteractionTracker.values())
        .reduce((sum, arr) => sum + arr.length, 0)
    };
  }
}

// 创建全局实例
const pageContentCollector = new PageContentCollector();

export default pageContentCollector;
export { PageContentCollector }; 
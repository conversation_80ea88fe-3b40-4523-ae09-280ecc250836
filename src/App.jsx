import React, { useState, useEffect, Suspense, lazy } from 'react';
import PixelLoader from './components/PixelLoader';
import Navbar from './components/Navbar';
import HeroSection from './components/HeroSection';
import FeaturesGuideSection from './components/FeaturesGuideSection';
import Footer from './components/Footer';
import BrowserCompatibilityCheck from './components/BrowserCompatibilityCheck';
import ResourcePreloader from './components/ResourcePreloader';
import ErrorBoundary from './components/ErrorBoundary';
import authManager from './services/authManager';
import './utils/emojiCompatibility'; // 引入emoji兼容性处理

// CopilotKit 导入
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotPopup } from '@copilotkit/react-ui';
import { getLdapApiUrl } from './config/apiConfig';
import '@copilotkit/react-ui/styles.css';
import EnhancedCopilotProvider from './components/EnhancedCopilotProvider';
import { CustomAssistantMessage } from './components/CopilotProvider';

// 懒加载非首屏组件 - 按需加载策略
const AIAssistantSection = lazy(() => import(/* webpackChunkName: "ai-assistant" */ './components/AIAssistantSection'));
const APIKeySection = lazy(() => import(/* webpackChunkName: "api-key" */ './components/APIKeySection'));
const DownloadsSection = lazy(() => import(/* webpackChunkName: "downloads" */ './components/DownloadsSection'));
const DocumentationSection = lazy(() => import(/* webpackChunkName: "documentation" */ './components/DocumentationSection'));
const NewsSection = lazy(() => import(/* webpackChunkName: "news" */ './components/NewsSection'));
const LoginModal = lazy(() => import(/* webpackChunkName: "login" */ './components/LoginModal'));

// 加载占位组件
const LoadingPlaceholder = ({ height = "500px" }) => (
  <div className={`flex items-center justify-center bg-gray-900/50 rounded-lg animate-pulse`} style={{ height }}>
    <div className="text-cyan-400 text-lg">Loading...</div>
  </div>
);

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [authMessage, setAuthMessage] = useState('');

  useEffect(() => {
    // 使用 authManager 检查用户登录状态
    const currentUser = authManager.getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
    }

    // 监听认证状态变化事件
    const handleAuthStateChange = (event) => {
      const { type, reason, remaining } = event.detail;
      
      switch (type) {
        case 'expired':
          setUser(null);
          setAuthMessage('登录已过期，请重新登录');
          setTimeout(() => setAuthMessage(''), 5000);
          break;
          
        case 'logout':
          setUser(null);
          if (reason === 'timeout') {
            setAuthMessage('登录超时，已自动注销');
            setTimeout(() => setAuthMessage(''), 5000);
          }
          break;
          
        case 'expiring':
          setAuthMessage(`登录将在${remaining.formattedTime}后过期`);
          setTimeout(() => setAuthMessage(''), 10000);
          break;
          
        default:
          break;
      }
    };

    // 监听CopilotKit触发的登录模态框事件
    const handleShowLoginModal = () => {
      setShowLoginModal(true);
    };

    window.addEventListener('authStateChange', handleAuthStateChange);
    window.addEventListener('showLoginModal', handleShowLoginModal);
    
    return () => {
      window.removeEventListener('authStateChange', handleAuthStateChange);
      window.removeEventListener('showLoginModal', handleShowLoginModal);
    };
  }, []);

  const handleLoaderComplete = () => {
    setIsLoading(false);
  };

  const handleLogin = (userData) => {
    // 使用 authManager 保存登录状态
    authManager.saveUserLogin(userData);
    setUser(userData);
    setShowLoginModal(false);
    
    // 显示登录成功消息
    const status = authManager.getLoginStatus();
    if (status.remaining && !status.remaining.expired) {
      setAuthMessage(`登录成功！登录有效期: ${status.remaining.formattedTime}`);
      setTimeout(() => setAuthMessage(''), 5000);
    }
  };

  const handleLogout = () => {
    // 使用 authManager 注销
    authManager.logout();
    setUser(null);
  };

  return (
    <ErrorBoundary>
      {/* 资源预加载 */}
      <ResourcePreloader />
      
      {/* 浏览器兼容性检测 */}
      <BrowserCompatibilityCheck />
      
      {/* 像素加载动画 */}
      <PixelLoader onComplete={handleLoaderComplete} />

      {/* 主内容 - 修复CopilotKit嵌套结构 */}
      {!isLoading && (
        <CopilotKit 
          runtimeUrl={`${getLdapApiUrl()}/api/copilotkit`}
          publicApiKey=""> {/* 空字符串，因为我们使用自己的后端代理 */}
          <EnhancedCopilotProvider user={user}>
            <div className="min-h-screen bg-black text-white">
              {/* 性能模式切换提示 */}
              {authMessage && (
                <div className="fixed top-4 right-4 z-40 bg-cyan-900/90 text-cyan-100 px-4 py-2 rounded-lg shadow-lg">
                  {authMessage}
                </div>
              )}
              
              {/* 导航栏 */}
              <Navbar 
                user={user} 
                onLogin={() => setShowLoginModal(true)} 
                onLogout={handleLogout}
              />

              {/* 主要内容区域 */}
              <main>
                {/* 英雄区域 */}
                <div id="home">
                  <HeroSection />
                </div>

                {/* 平台数据 */}
                <div id="features">
                  <FeaturesGuideSection />
                </div>

                {/* AI智能助手 */}
                <div id="ai-assistant">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <AIAssistantSection />
                  </Suspense>
                </div>

                {/* API密钥管理 */}
                <Suspense fallback={<LoadingPlaceholder />}>
                  <APIKeySection 
                    user={user} 
                    onLogin={() => setShowLoginModal(true)}
                  />
                </Suspense>

                {/* 工具下载 */}
                <div id="downloads">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <DownloadsSection 
                      user={user} 
                      onLogin={() => setShowLoginModal(true)}
                    />
                  </Suspense>
                </div>

                {/* 文档中心 */}
                <div id="docs">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <DocumentationSection />
                  </Suspense>
                </div>

                {/* 最新动态 */}
                <div id="news">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <NewsSection />
                  </Suspense>
                </div>
              </main>

              {/* 页脚 */}
              <Footer />
            </div>
            
            {/* 登录模态框 */}
            {showLoginModal && (
              <Suspense fallback={<div>Loading...</div>}>
                <LoginModal 
                  isOpen={showLoginModal}
                  onLogin={handleLogin}
                  onClose={() => setShowLoginModal(false)}
                />
              </Suspense>
            )}

          </EnhancedCopilotProvider>
          
          {/* CopilotPopup使用官方预设样式，并使用自定义AssistantMessage过滤思考内容 */}
          <CopilotPopup
            labels={{
              title: "🤖 YNNX AI助手 (增强版)",
              initial: "您好！👋 我是YNNX AI平台的增强版智能助手。\n\n✨ 增强功能特性：\n• 🧠 **上下文感知** - 理解当前页面内容和状态\n• 📚 **智能文档检索** - 搜索完整的开发者文档库\n• 🔍 **实时页面分析** - 分析页面结构和交互元素\n• 🧭 **智能导航** - 基于内容的精准导航\n• ⚡ **系统状态监控** - 实时监控平台状态\n\n💡 我现在可以更智能地帮助您：\n• 🔍 \"搜索文档：如何安装Cline插件\"\n• 📊 \"分析当前页面有什么功能\"\n• 🧭 \"导航到API密钥管理\"\n• 🔧 \"检查系统状态\"\n\n有什么可以为您效劳的吗？",
              placeholder: "输入您的问题，支持智能搜索、页面分析、导航等..."
            }}
            AssistantMessage={CustomAssistantMessage}
          />
        </CopilotKit>
      )}
    </ErrorBoundary>
  );
}

export default App; 